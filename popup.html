<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 15px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
    }

    .header {
      text-align: center;
      margin-bottom: 15px;
    }

    .header h1 {
      font-size: 16px;
      color: #333;
      margin: 0;
    }

    .status {
      padding: 8px;
      border-radius: 4px;
      margin-bottom: 10px;
      text-align: center;
      font-size: 13px;
    }

    .status.success {
      background-color: #d4edda;
      color: #155724;
    }

    .status.error {
      background-color: #f8d7da;
      color: #721c24;
    }

    .folder-info {
      background-color: #f8f9fa;
      padding: 8px;
      border-radius: 4px;
      margin-bottom: 10px;
      font-size: 13px;
    }

    .btn {
      width: 100%;
      padding: 10px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      background-color: #007bff;
      color: white;
      margin-bottom: 10px;
    }

    .btn:hover {
      background-color: #0056b3;
    }

    .btn:disabled {
      background-color: #6c757d;
      cursor: not-allowed;
    }

    .progress {
      margin-top: 10px;
      display: none;
    }

    .progress-bar {
      width: 100%;
      height: 15px;
      background-color: #e9ecef;
      border-radius: 8px;
      overflow: hidden;
    }

    .progress-fill {
      height: 100%;
      background-color: #28a745;
      width: 0%;
      transition: width 0.3s ease;
    }

    .progress-text {
      text-align: center;
      margin-top: 5px;
      font-size: 12px;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>导出有道云笔记</h1>
  </div>

  <div id="status" class="status" style="display: none;"></div>

  <div id="folder-info" class="folder-info" style="display: none;">
    <strong>文件夹：</strong><span id="folder-name">-</span><br>
    <strong>笔记数：</strong><span id="note-count">-</span>
  </div>

  <button id="export-btn" class="btn">导出为Markdown</button>

  <div id="progress" class="progress">
    <div class="progress-bar">
      <div id="progress-fill" class="progress-fill"></div>
    </div>
    <div id="progress-text" class="progress-text">准备中...</div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
