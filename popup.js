// popup.js - 简化版弹窗逻辑
document.addEventListener('DOMContentLoaded', async () => {
  const exportBtn = document.getElementById('export-btn');
  const statusDiv = document.getElementById('status');
  const folderInfo = document.getElementById('folder-info');
  const folderName = document.getElementById('folder-name');
  const noteCount = document.getElementById('note-count');
  const progress = document.getElementById('progress');
  const progressFill = document.getElementById('progress-fill');
  const progressText = document.getElementById('progress-text');

  // 检查当前页面
  const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

  if (!tab.url.includes('note.youdao.com')) {
    showStatus('请在有道云笔记网页版中使用', 'error');
    exportBtn.disabled = true;
    return;
  }

  // 获取页面信息
  try {
    const response = await chrome.tabs.sendMessage(tab.id, { action: 'getPageInfo' });

    if (response && response.success) {
      folderName.textContent = response.folderName || '未知文件夹';
      noteCount.textContent = response.noteCount || '0';
      folderInfo.style.display = 'block';

      if (response.noteCount > 0) {
        showStatus('找到 ' + response.noteCount + ' 篇笔记', 'success');
      } else {
        showStatus('当前文件夹没有笔记', 'error');
        exportBtn.disabled = true;
      }
    } else {
      showStatus('无法获取页面信息', 'error');
      exportBtn.disabled = true;
    }
  } catch (error) {
    showStatus('请刷新页面后重试', 'error');
    exportBtn.disabled = true;
  }

  // 导出按钮点击事件
  exportBtn.addEventListener('click', async () => {
    exportBtn.disabled = true;
    progress.style.display = 'block';
    showStatus('正在导出...', 'success');

    try {
      const response = await chrome.tabs.sendMessage(tab.id, {
        action: 'startExport',
        format: 'markdown'
      });

      if (!response || !response.success) {
        throw new Error(response?.error || '导出失败');
      }
    } catch (error) {
      showStatus('导出失败: ' + error.message, 'error');
      exportBtn.disabled = false;
      progress.style.display = 'none';
    }
  });

  // 监听导出进度
  chrome.runtime.onMessage.addListener((message) => {
    if (message.action === 'exportProgress') {
      progressFill.style.width = message.progress + '%';
      progressText.textContent = message.text;
    } else if (message.action === 'exportComplete') {
      handleExportComplete(message.data);
    } else if (message.action === 'exportError') {
      showStatus('导出失败: ' + message.error, 'error');
      exportBtn.disabled = false;
      progress.style.display = 'none';
    }
  });

  async function handleExportComplete(data) {
    progressFill.style.width = '100%';
    progressText.textContent = '导出完成！';
    showStatus('正在下载文件...', 'success');

    // 创建下载
    const blob = new Blob([data.content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);

    try {
      await chrome.downloads.download({
        url: url,
        filename: data.filename,
        saveAs: true
      });

      showStatus('下载完成！', 'success');
    } catch (error) {
      showStatus('下载失败', 'error');
    } finally {
      URL.revokeObjectURL(url);
      exportBtn.disabled = false;
      setTimeout(() => {
        progress.style.display = 'none';
      }, 2000);
    }
  }

  function showStatus(message, type) {
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
    statusDiv.style.display = 'block';
  }
});
