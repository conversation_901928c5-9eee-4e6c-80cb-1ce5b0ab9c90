# 刘雨晴个人周报汇总（2024年1月-7月）

```mermaid
mindmap
  root((LinkFlow消息列表性能优化))
    技术挑战
      不定高消息内容
        滚动抖动
        白屏问题
      大数据量场景
        内存占用高
        渲染性能差
      复杂消息类型
        文本/图片/文件
        Markdown渲染
    
    方案探索
      方案一：自研双缓冲
        技术思路
          视图池ViewPool
          回收池RecyclePool
          DOM复用策略
        优点
          性能理论最优
          完全可控
        缺点
          稳定性不足
          复用失效
          开发复杂
        结果：❌放弃
      
      方案二：react-virtuoso改造
        技术思路
          源码深度定制
          修复已知bug
          增加节流控制
        优点
          基于成熟方案
          针对性优化
        缺点
          滚动频闪
          性能提升有限
          维护成本高
        结果：❌放弃
      
      方案三：手写原生列表
        技术思路
          原生JavaScript
          双缓冲技术
          自定义滚动控制
        优点
          性能表现良好
          无框架依赖
        缺点
          React集成复杂
          工程化成本过高
          维护困难
        结果：❌放弃
      
      方案四：react-virtuoso+渲染优化
        技术思路
          成熟方案回归
          渲染层面优化
          参数精细调优
        优点
          稳定性高
          维护成本低
          生态兼容好
        缺点
          性能有上限
        结果：✅最终选择
    
    最终成果
      性能指标
        首屏渲染：800ms→300ms
        滚动帧率：30fps→60fps
        内存占用：150MB→80MB
      技术沉淀
        性能优化方法论
        最佳实践标准
        可复用技术方案
```

## 一、2024年1月

### 1.1 01.06-01.10
1. **平台建设**
   - **铃客平台**：完成运营管理室新年定制推文搭建；协助排查非信息技术部老师无法访问铃客平台文档问题，开通流量网关测试环境云桌面防火墙权限。
   - 技术学习：研读react-router和history相关源码，以及v5版本react-router-cache-route的keepalive实现原理，计划在oula上完善keep-alive功能。

2. **数字员工团队**
   - 优化operate状态图实时回显问题，提出更优解法并上线，同时向camunda提交pr。
   - 持续跟进批量表格助手需求。

3. **其他**
   - 与鲁老师团队沟通规则引擎平台背景。
   - 完成年终总结（链接：https://y2il0fdpuj.feishu.cn/docx/IvgydJv5woCXg5x4VHlca8zinAd）。


### 1.2 01.13-01.17
1. **协同办公**
   - 参考slack了解chatops需求背景及工作方向，讨论集成魔方的方案可行性。
   - 调研开源chat im工具库（@sendbird/uikit-react、@chatscope/chat-ui-kit-react等），尝试改造@sendbird/uikit-react组件，因耦合度高改为渐进式开发（提取可复用部分）。
   - 完成前端全局状态管理及架构设计，打通服务、@openim/wasm-client-sdk、前端全局状态管理与页面数据渲染通路。
   - 后续计划：并行开发状态流转与UI渲染模块。

2. **数字员工**
   - 完成数字员工服务平台铃客端parcel拆分与上线。
   - 沟通spreadjs组件优化需求，审阅批量表格处理前端设计文档，协助排查低代码生产apisix配置问题。

3. **任务工时**
   - 队列管理：创建子队列（0.3）、队列监控（0.5）。
   - 运营统计：label（0.2）、应用统计模板（3）、先锋应用弹窗（0.5）、运行方式（0.5）。
   - 工位信息优化：zeebe轮询（5s一次，0.5）。
   - 资产托管部页面复用与封装（0.5）。


## 二、2024年2月

### 2.1 02.06-02.07
1. **协同办公chat-im**
   - 优化消息收发机制：封装useHistoryMessageList和messageList，实现频道列表、消息列表等多场景复用；优化全局事件监听机制，支持各场景按conversation处理事件。
   - 性能优化：好友列表采用虚拟列表渲染，优化新建频道渲染效率。

2. **数字员工团队**
   - 参与新数字员工服务平台队列管理需求评审。
   - 开展批量表格处理需求评审及下迭代任务安排。


### 2.2 02.10-02.14
1. **协同办公chat-im（v0.1版本，测试链接：http://eipsit.htsc.com.cn/link-im/chat）**
   - 功能开发：实现聊天界面消息合并显示（同一头像下），特殊处理通知类消息；支持全局检索（群聊名称、id、好友姓名、工号）并跳转；侧边栏宽度可调，布局自适应；优化部分样式（参考slack）；处理引用消息回复事件。
   - 性能优化：增加聊天列表单次请求条数（20→50条），扩大虚拟列表buffer至500px；通过useMemo、useCallback减少子组件重绘；研究虚拟列表loadMoreMessage时的闪屏问题。
   - 调研：md编辑器组件封装与md消息预览方案。

2. **数字员工团队**
   - 评估企微聚合定制侧边栏需求。


### 2.3 02.17-02.21
1. **协同办公chat-im**
   - 性能优化：
     - 解决虚拟列表loadMoreMessage闪屏问题（动态更新firstItemIndex，或对隐藏消息渲染空item占位）。
     - 修复日期分隔后消息显示不全、定位失效问题（设置固定高度解决portal元素高度计算误差）。
     - 解决新建频道页面循环渲染崩溃问题（销毁useOnlineOrTypingStatus钩子监听器）。
     - 梳理通知类消息隐藏逻辑，提升可维护性。
   - 需求开发：实现消息列表按日期重组、快捷跳转最新消息、未读消息标记已读时机控制，优化基础样式与代码结构。
   - 技术调研：集成milkdown-mentions-plugin插件实现md编辑器@功能，确定reaction技术方案（前端触发→SDK通知→监听事件更新消息）。

2. **数字员工团队**
   - 完成企微聚合定制侧边栏需求评估、任务拆解与技术方案设计。
   - 对接i问组件库前端方案，协助排查低代码铃客生产apisix访问问题。


### 2.4 02.25-02.28
1. **协同办公chat-im**
   - 实现md mention插件@提醒功能。
   - 对接机器人指令：支持“/”调起指令，集成带参数指令的表单交互；处理enter事件优先级问题，完成群聊机器人指令集成（单聊因接口调整延迟至下迭代）。

2. **数字员工团队**
   - 提供i问组件库前端技术支持与指导。
   - 协助解决低代码铃客网关配置问题。


## 三、2024年3月

### 3.1 03.10-03.14
1. **协同办公chat-im**
   - 列表性能优化：
     - 尝试不定高虚拟列表（react-virtuoso源码，translate渲染），解决滚动过快重影问题。
     - 分离scroll滚动区与content内容区，通过“复制层”解决白屏问题（因高度计算限制暂未落地）。
     - 优化react-virtuoso方案：修复startReach事件不触发bug，为loadMoreMessage增加节流，减少不必要渲染，增加快速滑动时的“鱼骨”加载提示。
     - 尝试手写原生列表+双缓冲技术解决不定高问题。

2. **数字员工团队**
   - 参与企微客户洞察H5集成技术方案讨论与评审。


### 3.2 03.17-03.21
1. **协同办公chat-im**
   - 优化引用md消息样式，为LCRender增加原生加载动画。
   - 列表优化方案实践：
     - 方案一：拆分预加载区与渲染区数据，因数据转移逻辑复杂未落地。
     - 方案二：采用反向文档流，未使用虚拟列表，在消息搜索场景存在双向加载限制。
     - 方案三：参考企微双缓冲机制，实现视图池与回收池（DOM复用），解决子组件尺寸计算、不定高场景复用失效、size监听异常等问题，完成基础逻辑跑通。

2. **数字员工团队**
   - 持续参与企微客户洞察H5集成方案评审，评审word报告制作需求。

3. **其他**
   - 为铃客平台稽查团队提供选人组件技术支持。


### 3.3 03.24-03.28
1. **协同办公chat-im**
   - 集成react-virtuo-scroller（vue-virtuo-scroller的react本地化），解决消息列表问题：
     | 问题 | 原因 | 解决办法 |
     |---|---|---|
     | 列表无法滚动，元素定位异常 | 滚动事件闭包陷阱，使用旧方法与旧数据 | 解决闭包问题 |
     | 滚动时频闪、多次渲染 | visibleItem变化触发强制批量更新 | 移除强制更新逻辑 |
     | scrollToBottom无法触底 | 虚拟列表初次渲染无法获取总高度 | 首页加载20条数据，调用三次scrollToBottom |
     | loadMore时性能差 | messages数据洗牌影响DOM回收 | 持续优化中 |
   - 优化第一版方案：解决长文本消息显示异常，优化未读消息通知。
   - 沟通魔方加载效率提升方案与虚拟列表性能评测方案。

2. **其他**
   - 南京出差交流chatim相关工作，通过应知应会考试。


## 四、2024年4月

### 4.1 03.31-04.03
1. **协同办公chat-im**
   - 问题优化：
     - 解决滚到底&消息定位不准问题（针对非md消息关闭renderMd，提前初始化milkdown实例，使用单md实例渲染并微调样式）。
     - 修复bot键盘事件失效与发指令bug。
     - 梳理搜索消息定位逻辑，完成问题排查与自测。
   - 性能优化：接入魔方资源preload方案并评估性能，计划统一预加载后调试云桌面环境。

2. **其他**
   - 评审行知接入数字员工技术方案并评估工作量。


### 4.2 04.07-04.11
1. **协同办公chat-im**
   - md编辑器优化：
     - 处理单实例改造后的样式兼容（有序列表、无序列表、空格等）。
     - 解决列表输入后向上多跳行问题（移除trailing插件）。
     - 优化输入框滚动与mention弹窗定位（改为fixed定位）。
     - 修复普通文本换行渲染异常、编辑器状态切换跳动问题，统一编辑器与渲染器样式。
   - 列表优化：增加展开收起处理，优化新消息提示（基于atBottomStateChange事件），解决react-virtuoso初始化时多请求数据问题。
   - 问题排查：因milkdown自动升级导致消息发送异常，回退至v7.6.2版本。

2. **其他**
   - 推进行知接入数字员工的微前端改造与对接。
   - 参与编排方案需求讨论，协助优化operate状态回显数据并支持上线。


### 4.3 04.14-04.18
1. **协同办公chat-im**
   - md编辑器优化：限制无效内容发送（仅含符号、空格等），优化@弹窗样式与交互，调整@引用消息样式，修改代码块主题为浅色系，处理超链接弹窗显示逻辑，优化列表与引用格式。
   - 列表性能优化：调整初始化渲染buffer与count值（200条）避免多次请求，同步计算processedMessage减少更新次数，优化初始定位逻辑，将首次渲染时间控制在300ms内。
   - 其他：完成搜索模块代码review。

2. **数字员工**
   - 完成行知对接（apsix配置、登录联调）。
   - 确认编排画布自定义节点实现方案与modeler画布集成taie方案。
   - 解决taie-outward-web2.0在paas上的构建失败问题，支持批量表格处理spreadjs上线，评审尽调小助手二级网站技术方案。


### 4.4 04.21-04.25
1. **协同办公chat-im**
   - md编辑器优化：处理转发@消息的字段清理，支持@弹窗关闭后直接发送“@”，实现编辑器语法高亮，优化超链接弹窗交互，处理多行块元素引用与有序列表样式。
   - 列表问题：排查白屏问题，尝试假列表方案；解决搜索场景下加载限制与消息合并不一致问题（补充previousMessage字段）。
   - 其他：完成不同类型消息渲染的code review。

2. **数字员工**
   - 持续推进行知对接与apsix配置。
   - 优化金智维集成页面性能（taie-new-web拆包），解决新平台开发环境频繁登出问题，推进编排画布集成到taie，修复新官网returnUrl漏洞并上线。


### 4.5 04.28-04.30
1. **协同办公**
   - chat-im：修复复制html后无法删除单字符问题，优化右键复制逻辑（支持样式兼容）。
   - api-table：协助排查多维表格部署问题，了解项目背景、开发流程与打包方式。

2. **数字员工**
   - 提供ZB编排画布集成到taie的技术支持。
   - 了解ai automation背景，准备投入组件开发。


## 五、2024年5月

### 5.1 05.06-05.09
1. **协同办公chat-im**
   - md编辑器优化：处理链接类型节点bug，优化链接弹窗光标定位，重构parserMdHtml代码结构，研究mentionSchema自定义节点渲染（计划用于代码块自定义）。

2. **数字员工**
   - 沟通ai automation需求，确认开发方案，熟悉代码并启动开发。
   - 支持数字员工接入行知上生产（新建工作区、配置路由），协助数字员工服务平台部署eipnew上线。


### 5.2 05.12-05.16
1. **协同办公chat-im**
   - md编辑器调研：研究code-block、blockquote定制方案，计划全量私有化部署milkdown后改造，优化link节点弹窗问题，调整mention弹窗位置。
   - 其他：完成列表改造code review。

2. **数字员工**
   - 确认ai automation全流程交互细节与业务开发方案，评审bot注册与行知侧对接需求。

3. **平台建设**
   - 将ai automation组件库改造为模板组件库，完成ai step组件开发，调研学习mcp相关内容。


### 5.3 05.19-05.23
1. **协同办公chat-im**
   - Crepe编辑器工具集成与私有化：引入Milkdown的Crepe集成工具并私有化改造，采用核心模块（crepe、components）私有化策略，解决Web Component注册冲突（“Illegal constructor”），优化目录结构。
   - 自定义组件开发：重写link-tooltip组件替代默认实现，重构代码块schema（支持语言显示、皮肤选择），处理样式兼容与功能冲突。

2. **数字员工**
   - 持续跟进ai automation需求。


### 5.4 05.26-05.30
1. **协同办公chat-im**
   - Milkdown编辑器优化：解决私有化后的css兼容问题，统一编辑区与预览区主题，同步加载代码块语言包优化性能。
   - 大模型流式输出：参与技术方案评审，完成前端架构设计与发消息模块重构，开发核心组件。
   - apitable多维表格：协助搭建server流水线。

2. **数字员工**
   - 联调ai automation并优化step组件，评审迭代需求（行知对接、智能文本处理平台等）。


## 六、2024年6月

### 6.1 06.03-06.06
1. **协同办公**
   - 开发hiagent流式输出组件（预计6.16联调）。
   - 完成Milkdown编辑器私有化与代码块主题优化，修复样式问题。
   - 优化云文档在link内的打开逻辑，支持自有人员在云桌面外使用。

2. **数字员工**
   - 联调ai automation（测试中）。
   - 评审智能文本处理平台IDP需求并确定前端技术方案，开发企微助手侧边栏并处理鉴权问题。

3. **ai chrome插件**
   - 调研需求，搭建翻译助手智能体，确认sck与hiagent接口协议（计划下周对接）。


### 6.2 06.09-06.13
1. **协同办公linkflow**
   - 对接hiagent全链路联调（创建会话、接收/停止/更新流式消息、重新生成等）。
   - 特殊处理大模型流式消息中的参考文献（转换为可点击标签，避免侵入全部md渲染）。

2. **数字员工**
   - 解决ai automation联调问题（测试中）。
   - 评审行知对接审核页面、IDP智能文本处理平台、zeebe等迭代需求。

3. **平台能力建设**
   - ai chrome插件：优化代码结构（单次翻译创建一个会话，合并页面元素批量翻译），调优翻译智能体（处理特殊格式文本）。
   - 梳理mcp server与协同中心对接方案，优化ai automation的ai step组件。


### 6.3 06.16-06.20
1. **协同办公linkflow**
   - 完成hiagent全链路联调、测试与上线，解决多层级标题样式兼容、流式消息异常处理、复制功能优化（排除无关内容）、搜索结果显示、推荐问处理等问题。
   - 优化js wasm发包流程。

2. **平台工作**
   - 优化翻译智能体（处理中文翻译结果，调整合并字符数至1000，排除nav/footer标签翻译）。


### 6.4 06.23-06.27
1. **协同办公linkflow**
   - 优化im全局事件监听性能（批处理事件，优化会话列表与消息列表），文档：https://xcg1a1l6ku.feishu.cn/wiki/YlpCwIyrCixcHvkEgSXcGDZDn2c?from=from_copylink。
   - 完成80%的md渲染器图片自定义处理。

2. **数字员工**
   - 协助排查minio存储问题，评审IDP智能文本处理平台方案并提供技术支持，确定迭代需求排期。

3. **平台工作**
   - chrome ai插件：优化翻译功能（对比智普、豆包，文档：https://xcg1a1l6ku.feishu.cn/wiki/Y0MZwxGRUiakvakflekcXGWzndd），解决渲染不全与嵌套标签问题。


## 七、2024年7月

### 7.1 06.30-07.04
1. **协同办公linkflow**
   - 实现代码块复制功能（文档：http://gitlab.htzq.htsc.com.cn/openim/link-im/-/blob/release-0.13.0/docs/%E4%BB%A3%E7%A0%81%E5%9D%97%E5%A4%8D%E5%88%B6.md），处理md编辑器图片定高与加载失败兜底。
   - 排查压测优化后流式消息更新失败问题（暂取消批处理，下迭代针对性优化）。
   - 编写md自定义语法文档（http://gitlab.htzq.htsc.com.cn/openim/link-im/-/blob/release-0.13.0/docs/milkdown%E8%87%AA%E5%AE%9A%E4%B9%89%E8%AF%AD%E6%B3%95.md），为getCleanText方法增加单元测试。

2. **数字员工**
   - 评估与设计speadjs自定义检查表通用组件方案。

3. **平台工作**
   - 调研联网查询方案，接入智能体联网查询插件（文档：https://xcg1a1l6ku.feishu.cn/wiki/DcyQwkqWziDeOXkrJXZcd64gnlf）。


### 7.2 07.07-07.11
1. **协同办公linkflow**
   - 优化renderMd渲染后li标签的展示与交互（样式适配、流式数据渲染），设计性能优化方案。
   - 协助排查云文档测试环境埋点上报量过大问题。

2. **数字员工**
   - 参与bot注册需求评审（注册流程、权限控制、交互逻辑）。

3. **平台工作**
   - AI浏览器插件：调研langChain与百度千帆AI搜索结合方案，梳理核心功能清单，撰写需求文档v1.0。


### 7.3 07.14-07.18
1. **协同办公linkflow**
   - 跟进bot取消后处理问题，优化编辑器群公告编辑态默认值塞值逻辑，修复自定义列表复制样式bug。
   - 评审bot多模态文件解析需求（多文件分条发送、携带subConversationId、上传后触发sendMessageToBot）。

2. **数字员工**
   - 配置ai-automation路由转发并协助上线，支持批量表格处理上线。

3. **平台工作**
   - AI浏览器插件：撰写翻译模块需求文档，参与架构设计，调研chatbox、langGraph、多智能体架构。


### 7.4 07.21-07.25
1. **协同办公linkflow**
   - 开发bot文件解析功能（上传→发送文件消息→发送流式消息流程），完成bot的think-message event联调并部署至sit环境。
   - 参与linQ执行状态回显方案设计（回显内容、触发条件等），确认linkflow bot后台管理与魔方对接方案并提供协助，协助解决客户端windows截图问题。

2. **平台工作**
   - AI浏览器插件：完成web-assistant bff一期接口（会话管理、用户配置）联调与部署，设计用户信息表结构。